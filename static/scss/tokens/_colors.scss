:root {
    // --- Base & Neutral Colors (from your provided screenshot) ---
    --color-base-white: #FCFCFC;
    --color-base-black: #080808;
    --color-neutrals-100: #E8EBED;
    --color-neutrals-200: #E2E2E2;
    --color-neutrals-300: #C8C8C8;
    --color-neutrals-400: #B2B2B2;
    --color-neutrals-500: #939393;
    --color-neutrals-600: #7A7A7A;
    --color-neutrals-700: #4F4F4F;
    --color-neutrals-800: #3B3B3B;
    --color-neutrals-900: #2C2C2C;
    --color-neutrals-1000: #141414;
    
    // --- Branding Greens (from your provided screenshot) ---
    --color-green-50: #003B34;
    --color-green-100: #1A3134;
    --color-green-200: #004D43;
    --color-green-300: #045B50;
    --color-green-400: #1B7A65;
    --color-green-500: #2CA88C;
    --color-green-600: #66BB94;
    --color-green-700: #C7EAE6;
    --color-green-800: #A9CBC4;
    --color-green-900: #ECF5F4;
    --color-green-1000: #3F5852;
    --color-green-1100: #DAEDEB;

    // --- Semantic Colors (from your provided screenshot) ---
    // Success
    --color-success: #00C60A;
    --color-success-100: #009908;
    --color-success-200: #005D05;
    --color-success-300: #27FF32;

    // Warning
    --color-warning: #F4C790;
    --color-warning-100: #F4C790;
    --color-warning-200: #EDA145;
    --color-warning-300: #CC7914;

    // Error
    --color-error: #E4626F;
    --color-error-100: #E4626F;
    --color-error-200: #C03744;
    --color-error-300: #8C1823;
    
    // --- Stroke Colors (from your provided screenshot) ---
    --color-stroke-green: #146A61;
    --color-stroke-light: #E8F0EF;
    --color-stroke-grey: #C6C6C6;
    --color-stroke-light-green: #64BEAA;
}